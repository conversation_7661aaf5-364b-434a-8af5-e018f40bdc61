import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import planApi from '@/service/inspection/planApi';
import { <PERSON><PERSON>, Spin } from 'antd';
import dayjs from 'dayjs';
import baseApi, { DictionaryItem } from '@/service/baseApi';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import { InspectionPlanVo, PlanInsertParam, PlanUpdateParam } from '@/types/inspection/plan';
import { ActionType } from 'yth-ui/es/components/list';
import { BaseResponse } from '@/types/common';
import type { Form } from '@formily/core/esm/models';
import { CurrentUser } from '@/Constant';
import { Unit, User } from '@/service/system';
import LocationWithDraw from '@/components/LocationWithDraw';
import dicParams from '../dicParams';
import style from '../index.module.less';

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗的类别 add 新增 view 查看 edit 编辑 */
  type: string;
  /** 弹窗传入的数据 */
  dataObj: { id?: string; [key: string]: React.Key };
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const InspectionPlanModal: React.FC<PropsTypes> = ({ type, dataObj, closeModal = () => {} }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false); // 标记是否正在请求数据
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false); // 标记是否已请求过数据
  const currentRef: React.Ref<
    | (ActionType & {
        delRow: (name: string, index: number) => void;
        addRows: (name: string, data: object[]) => void;
        getSelData: () => string[]; // 添加缺失的 getSelData 方法，返回选中的数据ID数组
      })
    | undefined
  > = React.useRef();
  const [planType, setPlanType] = useState<string>(''); // 计划类型
  const [inspectionMethodType, setInspectionMethodType] = useState<string>(''); // 巡检方式
  const [cantEdit, setCantEdit] = useState<boolean>(false); // 不可编辑

  // 表单
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    try {
      const res: BaseResponse<InspectionPlanVo> = await planApi.getDetailById({
        id: dataObj.id,
      });
      if (res && res.code && res.code === 200) {
        const formD: InspectionPlanVo = res.data;
        const newData: Record<string, unknown> = {};
        setPlanType(formD.planType);
        // 单位
        newData.executionFrequency = [
          { code: formD?.executionFrequency, text: formD?.executionFrequencyText },
        ];
        // 负责人
        newData.directorUserName = [{ id: formD?.directorUserId, name: formD?.directorUserName }];
        // 巡检方式
        newData.inspectionMethod = [
          { code: formD?.inspectionMethod, text: formD?.inspectionMethodText },
        ];
        // 巡检人
        newData.partyUserIds = (formD.partyUserList as Record<string, string>[])?.map((item) => {
          return { ...item, name: item.realName || '', type: 'user' };
        });
        // 计划类型
        newData.planType = [{ code: formD?.planType, text: formD?.planTypeText }];
        // 计划类型
        newData.planType = [{ code: formD?.planType, text: formD?.planTypeText }];
        // 是否短信
        newData.isRemind = [
          { code: String(formD?.isRemind), text: formD?.isRemind === 1 ? '是' : '否' },
        ];
        // 是否启用
        newData.isUsed = [
          { code: String(formD?.isUsed), text: formD?.isUsed === 1 ? '启用' : '停用' },
        ];
        // 巡检点位
        newData.list = formD.pointsList;
        // 初次巡检时间
        newData.validDate = formD.validStartDate;
        // 循环巡检计划时间
        newData.taskDate = [formD.taskStartDate, formD.taskEndDate];
        // 单次巡检计划时间
        newData.taskDatetime = [
          `${formD.validStartDate} ${formD.taskStartDate}`,
          `${formD.validEndDate} ${formD.taskEndDate}`,
        ];
        setInspectionMethodType(formD.inspectionMethod);
        if (formD.lastCreateTaskDate) {
          setCantEdit(true);
        } else if (formD.inspectionMethod === dicParams.INSPECTION_METHOD_UAV) {
          setCantEdit(true);
        } else if (type === 'view') {
          setCantEdit(true);
        } else {
          setCantEdit(false);
        }

        form.setValues({ ...formD, ...newData });
        setIsLoading(false);
      }
    } catch {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (type && (type === 'edit' || type === 'view')) {
      queryDataDetail().finally(() => {
        setIsCasRequested(true);
      });
    } else {
      setIsCasRequested(true);
    }
  }, [type, dataObj]);

  // 点击取消
  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  // 新增保存
  const submitAddData: (data: PlanInsertParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    try {
      const res: BaseResponse<object> = await planApi.insert(data);
      if (res && res.code && res.code === 200) {
        closeModal();
      }
      setIsLoading(false);
    } catch {
      setIsLoading(false);
    }
  };

  // 编辑保存
  const submitEditData: (data: PlanUpdateParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    try {
      const res: BaseResponse<object> = await planApi.update({ ...data, id: dataObj?.id });
      if (res && res.code && res.code === 200) {
        closeModal();
      }
      setIsLoading(false);
    } catch {
      setIsLoading(false);
    }
  };

  // 点击保存
  const save: () => Promise<void> = async () => {
    form.validate().then(() => {
      const submitData: PlanInsertParam = { ...form.values };
      // 计划类型
      submitData.planType = form.values?.planType?.[0]?.code;

      // 单次计划需要将巡检计划时间 拆分为 日期和时间
      if (planType === 'A27A02A02') {
        // 巡检计划开始结束时间  初次巡检时间
        if (form.values?.taskDatetime) {
          submitData.taskStartDate = dayjs(form.values?.taskDatetime?.[0]).format('HH:mm');
          submitData.taskEndDate = dayjs(form.values?.taskDatetime?.[1]).format('HH:mm');
          submitData.validStartDate = dayjs(form.values?.taskDatetime?.[0]).format('YYYY-MM-DD');
          submitData.validEndDate = dayjs(form.values?.taskDatetime?.[1]).format('YYYY-MM-DD');
        }
      } else {
        // 巡检计划开始结束时间
        if (form.values?.taskDate) {
          submitData.taskStartDate = form.values?.taskDate?.[0];
          submitData.taskEndDate = form.values?.taskDate?.[1];
        }
        // 初次巡检时间
        if (form.values?.validDate) {
          submitData.validStartDate = form.values?.validDate;
          // submitData.validEndDate = form.values?.validDate;
        }
      }

      // 修改巡检人id成字符串
      const changeIds: () => string = () => {
        try {
          return (
            form.values?.partyUserIds?.map((item: Record<string, string>) => item.id) ?? []
          ).join(',');
        } catch {
          return '';
        }
      };

      // 巡检方式
      submitData.inspectionMethod = form.values?.inspectionMethod?.[0]?.code;

      // 单位
      submitData.executionFrequency = form.values?.executionFrequency?.[0]?.code;
      // 是否短信提醒
      submitData.isRemind = form.values?.isRemind?.[0]?.code;
      // 计划是否启用
      submitData.isUsed = form.values?.isUsed?.[0]?.code;
      // 负责人
      submitData.directorUserName = form.values?.directorUserName?.[0]?.realName;
      submitData.directorUserId = form.values?.directorUserName?.[0]?.id;
      // 巡检人 多个
      submitData.partyUserIds = changeIds();
      // 巡检点
      submitData.pointList = form.values?.list;

      // 默认即刻生成任务（实际后端会判断是否应该生成，应该指单次任务当天开始，或者循环任务当天开始 并且开始时间在当前时间之后）
      submitData.atOnceCreateTask = true;

      if (type === 'add') {
        submitAddData(submitData);
      } else if (type === 'edit') {
        submitEditData(submitData);
      }
    });
  };

  return (
    <div className={style['yth-inspection-moduel']}>
      <Spin spinning={isLoading}>
        {isCasRequested && (
          <YTHForm form={form} col={2}>
            <YTHForm.Item
              name="id"
              title="id"
              labelType={2}
              required={false}
              display="hidden"
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />
            <YTHForm.Item
              name="planName"
              title="	计划名称"
              labelType={2}
              required
              componentName="Input"
              componentProps={{
                disabled: cantEdit,
                placeholder: '请输入',
              }}
            />
            <YTHForm.Item
              name="planCode"
              title="计划编码"
              labelType={2}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '保存后自动创建',
              }}
            />
            <YTHForm.Item
              name="inspectionMethod"
              title="巡检方式"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const data: DictionaryItem[] = await baseApi.getDictionary(
                    dicParams.INSPECTION_METHOD,
                  );
                  // 过滤掉  无人机巡检
                  const newData: DictionaryItem[] = data.map((item) => {
                    if (item.code === dicParams.INSPECTION_METHOD_UAV) {
                      return {
                        ...item,
                        disabled: true,
                      };
                    }
                    return item;
                  });
                  return newData;
                },
                disabled: cantEdit,

                p_props: {
                  placeholder: '请选择',
                },
                onChange: (e: DictionaryItem[]) => {
                  setInspectionMethodType(e[0]?.code);
                },
              }}
            />
            <YTHForm.Item
              name="planType"
              title="计划类型"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: () => {
                  return baseApi.getDictionary(dicParams.PLAN_TYPE);
                },
                disabled: cantEdit,
                p_props: {
                  placeholder: '请选择',
                },
                onChange: (val: DictionaryItem[]) => {
                  setPlanType(val[0].code);
                  // 判断循环计划显示单位
                  form.query('executionFrequency')?.forEach((item) => {
                    if (val[0].code === 'A27A02A01') {
                      item.setDisplay('visible');
                    } else {
                      item.setDisplay('hidden');
                    }
                  });
                  // 判断循环计划显示周期
                  form.query('frequencyNumber')?.forEach((item) => {
                    if (val[0].code === 'A27A02A01') {
                      item.setDisplay('visible');
                    } else {
                      item.setDisplay('hidden');
                    }
                  });
                  // 判断循环计划显示初次巡检时间
                  form.query('validDate')?.forEach((item) => {
                    if (val[0].code === 'A27A02A01') {
                      item.setDisplay('visible');
                    } else {
                      item.setDisplay('hidden');
                    }
                  });
                  // 判断循环计划显示初次巡检时间
                  form.query('taskDate')?.forEach((item) => {
                    if (val[0].code === 'A27A02A01') {
                      item.setDisplay('visible');
                    } else {
                      item.setDisplay('hidden');
                    }
                  });
                  // 判断循环计划显示初次巡检时间
                  form.query('taskDatetime')?.forEach((item) => {
                    if (val[0].code === 'A27A02A02') {
                      item.setDisplay('visible');
                    } else {
                      item.setDisplay('hidden');
                    }
                  });
                  form.setValues({
                    taskDate: [],
                    taskDatetime: [],
                  });
                },
              }}
            />
            {planType === 'A27A02A01' && (
              <YTHForm.Item
                name="executionFrequency"
                title="单位"
                labelType={2}
                required
                componentName="Selector"
                componentProps={{
                  request: () => {
                    return baseApi.getDictionary(dicParams.EXECUTION_FREQUENCY);
                  },
                  disabled: cantEdit,

                  p_props: {
                    placeholder: '请选择',
                  },
                }}
              />
            )}
            {planType === 'A27A02A01' && (
              <YTHForm.Item
                name="frequencyNumber"
                title="周期"
                labelType={2}
                required
                componentName="InputNumber"
                componentProps={{
                  disabled: cantEdit,
                }}
              />
            )}
            <YTHForm.Item
              name="directorUserName"
              title="负责人"
              labelType={2}
              required
              componentName="PickUser"
              componentProps={{
                defaultOrganize: {
                  id: CurrentUser()?.unitId,
                  name: '',
                  type: 'org',
                },
                requestOrganize: async () => {
                  const resData: Unit = await baseApi.getUnitTree();
                  return formatTree(resData, 'unitType', 'unitName');
                },
                requestUser: async (organize: Unit) => {
                  const resData: User[] = await baseApi.getUserList(organize.id);
                  const newUsers: User[] = resData?.map((item) => {
                    return { ...item, name: item.realName, type: 'user' };
                  });
                  return newUsers;
                },
                multiple: false,
                disabled: cantEdit,
              }}
            />
            <YTHForm.Item
              name="partyUserIds"
              title="巡检人"
              labelType={2}
              required
              componentName="PickUser"
              componentProps={{
                defaultOrganize: {
                  id: CurrentUser()?.unitId,
                  name: '',
                  type: 'org',
                },
                requestOrganize: async () => {
                  const resData: Unit = await baseApi.getUnitTree();
                  return formatTree(resData, 'unitType', 'unitName');
                },
                requestUser: async (organize: Unit) => {
                  const resData: User[] = await baseApi.getUserList(organize.id);
                  const newUsers: User[] = resData?.map((item) => {
                    return { ...item, name: item.realName, type: 'user' };
                  });
                  return newUsers;
                },
                multiple: true,
                disabled: cantEdit,
              }}
            />
            {planType === 'A27A02A01' && (
              <YTHForm.Item
                name="validDate"
                title="初次巡检时间"
                required
                labelType={2}
                componentName="DatePicker"
                componentProps={{
                  placeholder: '',
                  precision: 'day',
                  formatter: 'YYYY-MM-DD',
                  disabled: cantEdit,
                }}
              />
            )}
            {planType === 'A27A02A01' && (
              <YTHForm.Item
                name="taskDate"
                title="巡检计划时间"
                required
                labelType={2}
                componentName="DatePicker"
                componentProps={{
                  range: true,
                  type: 'time',
                  formatter: 'HH:mm',
                  disabled: cantEdit,
                }}
              />
            )}
            {/* 单次计划显示 */}
            {planType === 'A27A02A02' && (
              <YTHForm.Item
                name="taskDatetime"
                title="巡检计划时间"
                required
                labelType={2}
                componentName="DatePicker"
                componentProps={{
                  range: true,
                  type: 'datetime',
                  formatter: 'YYYY-MM-DD HH:mm',
                  disabled: cantEdit,
                }}
              />
            )}
            <YTHForm.Item
              name="points"
              title="巡检路线"
              labelType={2}
              required
              component={LocationWithDraw}
              componentProps={{
                operateType:
                  cantEdit || inspectionMethodType === dicParams.INSPECTION_METHOD_UAV
                    ? 'view'
                    : 'add',
                form,
                mapType: 'line',
              }}
            />
            <YTHForm.Item
              name="isRemind"
              title="是否短信提醒"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: () => {
                  return [
                    { code: '1', text: '是' },
                    { code: '0', text: '否' },
                  ];
                },
                disabled: cantEdit,
                p_props: {
                  placeholder: '请选择',
                },
              }}
            />
            <YTHForm.Item
              name="isUsed"
              title="计划是否启用"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: () => {
                  return [
                    { code: '1', text: '启用' },
                    { code: '0', text: '停用' },
                  ];
                },
                disabled:
                  inspectionMethodType === dicParams.INSPECTION_METHOD_UAV ? true : type === 'view',
                p_props: {
                  placeholder: '请选择',
                },
              }}
            />
            <YTHForm.Item
              name="remark"
              title="备注"
              labelType={2}
              componentName="Input"
              componentProps={{
                disabled:
                  inspectionMethodType === dicParams.INSPECTION_METHOD_UAV ? true : type === 'view',
              }}
            />
            <YTHForm.List
              title="巡检地点"
              columns={[
                {
                  title: '检查点名称',
                  name: 'pointName',
                  minWidth: 80,
                  fixed: 'left',
                  edit: !cantEdit,
                  componentName: 'Input',
                  componentProps: {},
                  required: true,
                },
                {
                  title: '巡检内容',
                  name: 'inspectionContent',
                  minWidth: 80,
                  fixed: 'left',
                  edit: !cantEdit,
                  componentName: 'Input',
                  componentProps: {},
                  required: true,
                },
                {
                  title: '检查点坐标',
                  name: 'pointLocation',
                  minWidth: 80,
                  fixed: 'left',
                  edit: !cantEdit,
                  component: LocationWithDraw,
                  componentProps: (_: Record<string, unknown>, index: number) => ({
                    operateType:
                      cantEdit || inspectionMethodType === dicParams.INSPECTION_METHOD_UAV
                        ? 'view'
                        : 'add',
                    form,
                    mapType: 'point',
                    currentRowIndex: index, // 传递当前行索引
                  }),
                  required: true,
                },
              ]}
              name="list"
              rowOperations={[
                {
                  key: `list_key_${String(new Date())}`,
                  title: '删除',
                  type: 'danger',
                  disabled: () => cantEdit,
                  operation: (index) => {
                    currentRef?.current.delRow('list', index + 1);
                  },
                },
              ]}
              extra={[
                {
                  key: 'addData',
                  title: '添加数据',
                  type: 'main',
                  disabled: cantEdit,
                  operation: () => {
                    currentRef?.current.addRows('list', [{}]);
                  },
                },
              ]}
              actionRef={currentRef}
            />
          </YTHForm>
        )}
        <div className={style['modal-operation']}>
          {(type === 'add' || type === 'edit') && (
            <Button onClick={save} type="primary">
              保存
            </Button>
          )}
          <Button onClick={cancel} className={style['reset-btn']}>
            取消
          </Button>
        </div>
      </Spin>
    </div>
  );
};
export default InspectionPlanModal;
