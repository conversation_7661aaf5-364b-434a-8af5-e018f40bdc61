import React, { useState, useEffect } from 'react';
import { YTHDatePicker, YTHForm, YTHToast } from 'yth-ui';
import taskApi from '@/service/taskApi';
import { message, Button, Modal, Input } from 'antd';
import { CurrentUser, Token } from '@/Constant';
import baseApi from '@/service/baseApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import { BaseResponse } from '@/types/common';
import { TaskPointsVo } from '@/types/task';
import { Unit, User } from '@/service/system';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import style from '../task.module.less';

const { TextArea } = Input;

interface CheckPointEditModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (point: TaskPointsVo, isEdit: boolean, index: number) => void;
  editingPoint?: TaskPointsVo;
  editingIndex: number;
  modalType: 'add' | 'edit' | 'view';
  taskId: string;
  inspectionMethod: string;
}

const CheckPointEditModal: React.FC<CheckPointEditModalProps> = ({
  visible,
  onClose,
  onSuccess,
  editingPoint,
  editingIndex,
  modalType,
  taskId,
  inspectionMethod,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentPointCheckStatus, setCurrentPointCheckStatus] = useState<number>(0);
  const [currentPointCheckResult, setCurrentPointCheckResult] = useState<number>(1);
  const [currentPointIsNeedHandle, setCurrentPointIsNeedHandle] = useState<number>(0);

  const pointForm: ReturnType<typeof YTHForm.createForm> = React.useMemo(
    () => YTHForm.createForm({}),
    [],
  );

  // 初始化表单数据
  useEffect(() => {
    if (visible && editingPoint && modalType !== 'add') {
      const newRecord: TaskPointsVo = {
        ...editingPoint,
        handleUserName: [
          { id: editingPoint?.handleUserId, name: editingPoint?.handleUserIdText || '' },
        ],
      };

      setCurrentPointCheckStatus((editingPoint.checkStatus || 0) as number);
      setCurrentPointCheckResult((editingPoint.checkResult || 1) as number);
      setCurrentPointIsNeedHandle((editingPoint.isNeedHandle || 0) as number);

      // 处理检查状态
      if (typeof editingPoint.checkStatus === 'number') {
        newRecord.checkStatus = [
          {
            code: editingPoint?.checkStatus,
            value: editingPoint?.checkStatus,
            text: `${editingPoint?.checkStatus}`,
          },
        ];
      }

      // 处理检查结果
      if (typeof editingPoint.checkResult === 'number') {
        newRecord.checkResult = [
          {
            code: editingPoint?.checkResult,
            value: editingPoint?.checkResult,
            text: `${editingPoint?.checkResult}`,
          },
        ];
      }

      // 处理异常是否需要处置
      if (typeof editingPoint.isNeedHandle === 'number') {
        newRecord.isNeedHandle = [
          {
            code: editingPoint?.isNeedHandle,
            value: editingPoint?.isNeedHandle,
            text: `${editingPoint?.isNeedHandle}`,
          },
        ];
      }

      // 处理附件
      newRecord.attachments = newRecord?.attachments ? JSON.parse(newRecord.attachments) : [];

      pointForm.setValues(newRecord);
    } else if (visible && modalType === 'add') {
      // 新增模式下重置状态
      setCurrentPointCheckStatus(0);
      setCurrentPointCheckResult(1);
      setCurrentPointIsNeedHandle(0);
      pointForm.reset();
    }
  }, [visible, editingPoint, modalType]);

  // 校验失败提示
  const showFormRequiredFail: () => void = () => {
    YTHToast.show({
      type: 'error',
      messageText: '表格信息不正确,请按照提示填写',
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
  };

  // 处理检查结果变化
  const handleCheckResultChange: (value: unknown) => void = (value: unknown): void => {
    let checkResult: number = 0;
    if (Array.isArray(value)) {
      checkResult = Number(value[0]?.code);
    } else if (typeof value === 'number') {
      checkResult = value;
    }
    setCurrentPointCheckResult(checkResult);
  };

  // 处理异常是否需要处置变化
  const handleIsNeedHandleChange: (value: unknown) => void = (value: unknown): void => {
    let isNeedHandle: number = 0;
    if (Array.isArray(value)) {
      isNeedHandle = Number(value[0]?.code);
    } else if (typeof value === 'number') {
      isNeedHandle = value;
    }
    setCurrentPointIsNeedHandle(isNeedHandle);
  };

  // 确定保存
  const handlePointOk: (checkStatus: number) => Promise<void> = async (checkStatus: number) => {
    const res: string = await pointForm.validate();
    if (res !== 'fail') {
      const pointData: TaskPointsVo = JSON.parse(JSON.stringify(pointForm.values));
      pointData.checkStatus = checkStatus;
      pointData.taskId = taskId;

      // 检查结果 1 正常 0 异常
      if (Array.isArray(pointData.checkResult)) {
        pointData.checkResult = pointData?.checkResult?.[0]?.code as number;
      }

      // 异常是否需要处置 1 是 0 否
      if (Array.isArray(pointData.isNeedHandle)) {
        pointData.isNeedHandle = pointData?.isNeedHandle?.[0]?.code as number;
      }

      // 处置人
      if (Array.isArray(pointData.handleUserName)) {
        pointData.handleUserId = pointData?.handleUserName?.[0]?.id;
      }

      // 处理附件
      if (pointData.attachments) {
        pointData.attachments = JSON.stringify(pointData.attachments);
      }

      // 检查结果 1 正常 0 异常
      if (currentPointCheckResult === 0) {
        if (typeof pointData.isNeedHandle === 'undefined') {
          message.error('请确认异常是否需要进行处置');
          return;
        }
        if (currentPointIsNeedHandle === 1) {
          if (!pointData.handleUserId) {
            message.error('请选择异常处置人');
            return;
          }
          if (!pointData.handleRequire) {
            message.error('请输入异常处置要求');
            return;
          }
        }
      }
      try {
        setIsLoading(true);
        if (editingIndex >= 0) {
          // 编辑
          const addRs: BaseResponse<object> = await taskApi.updateTaskPoint(pointData);
          if (addRs && addRs.code && addRs.code === 200) {
            message.success('编辑检查点成功');
            onSuccess(addRs.data as TaskPointsVo, true, editingIndex);
            onClose();
          } else {
            message.error('编辑检查点失败');
          }
        } else {
          // 新增
          const addRs: BaseResponse<object> = await taskApi.insertTaskPoint(pointData);
          if (addRs && addRs.code && addRs.code === 200) {
            message.success('新增检查点成功');
            onSuccess(addRs.data as TaskPointsVo, false, -1);
            onClose();
          } else {
            message.error('新增检查点失败');
          }
        }
        pointForm.reset();
      } finally {
        setIsLoading(false);
      }
    } else {
      showFormRequiredFail();
      setIsLoading(false);
    }
  };

  const handleClose: () => void = () => {
    pointForm.reset();
    onClose();
  };

  return (
    <Modal
      width="900px"
      title={
        (modalType === 'add' ? '添加' : modalType === 'edit' ? '编辑' : '查看') +
        (dicParams.UAV === inspectionMethod ? '' : '检查点')
      }
      visible={visible}
      okText="保存"
      cancelText="取消"
      onCancel={handleClose}
      destroyOnClose
      footer={[
        // 未检查
        modalType !== 'view' && currentPointCheckStatus === 0 && (
          <Button
            key="save"
            onClick={() => handlePointOk(0)}
            className={style['search-btn']}
            type="primary"
            loading={isLoading}
          >
            保存
          </Button>
        ),

        modalType !== 'view' && currentPointCheckStatus === 0 && currentPointIsNeedHandle === 1 && (
          <Button
            key="saveAndSubmit"
            onClick={() => handlePointOk(1)}
            className={style['search-btn']}
            type="primary"
            loading={isLoading}
          >
            保存并提交
          </Button>
        ),
        <Button key="cancel" onClick={handleClose} className={style['reset-btn']}>
          取消
        </Button>,
      ]}
    >
      <YTHForm form={pointForm} col={2}>
        <YTHForm.Item
          name="pointName"
          title="检查点名称"
          labelType={2}
          required
          disabled={modalType === 'view' || dicParams.UAV !== inspectionMethod}
          componentName="Input"
          componentProps={{
            placeholder: '请输入检查点名称',
          }}
        />
        <YTHForm.Item
          name="pointLocation"
          title="检查点坐标"
          labelType={2}
          required
          disabled={modalType === 'view'}
          componentName="Input"
          componentProps={{
            placeholder: '请输入检查点坐标',
          }}
        />
        <YTHForm.Item
          name="inspectionContent"
          title="巡检内容"
          labelType={2}
          required
          disabled={modalType === 'view' || dicParams.UAV !== inspectionMethod}
          component={TextArea}
          componentProps={{
            placeholder: '请输入巡检内容',
            rows: 3,
          }}
          mergeRow={2}
        />
        <YTHForm.Item
          name="checkResult"
          title="检查结果"
          required
          disabled={modalType === 'view'}
          labelType={2}
          componentName="Selector"
          componentProps={{
            request: async () => {
              return [
                { code: 1, text: '正常' },
                { code: 0, text: '异常' },
              ];
            },
            p_props: {
              placeholder: '请选择检查结果',
            },
            onChange: (value: number) => {
              handleCheckResultChange(value);
            },
          }}
        />
        {currentPointCheckResult === 0 && (
          <>
            <YTHForm.Item
              name="exceptionDescribe"
              title="异常描述"
              labelType={2}
              required
              disabled={modalType === 'view'}
              component={TextArea}
              componentProps={{
                placeholder: '请输入异常描述',
                rows: 2,
              }}
              mergeRow={2}
            />
            <YTHForm.Item
              name="isNeedHandle"
              title="异常是否需要处置"
              labelType={2}
              componentName="Selector"
              disabled={modalType === 'view'}
              componentProps={{
                request: async () => {
                  return [
                    { code: 0, text: '不需要处置' },
                    { code: 1, text: '需要处置' },
                  ];
                },
                p_props: {
                  placeholder: '请选择异常是否需要处置',
                },
                onChange: (value: number) => {
                  handleIsNeedHandleChange(value);
                },
              }}
            />
          </>
        )}
        {currentPointIsNeedHandle === 1 && (
          <>
            <YTHForm.Item
              name="handleUserName"
              title="处置人"
              labelType={2}
              disabled={modalType === 'view'}
              componentName="PickUser"
              componentProps={{
                defaultOrganize: {
                  id: CurrentUser()?.unitId,
                  name: '',
                  type: 'org',
                },
                requestOrganize: async () => {
                  const resData: Unit = await baseApi.getUnitTree();
                  return formatTree(resData, 'unitType', 'unitName');
                },
                requestUser: async (organize: Unit) => {
                  const resData: User[] = await baseApi.getUserList(organize.id);
                  const newUsers: User[] = resData?.map((item) => {
                    return { ...item, name: item.realName, type: 'user' };
                  });
                  return newUsers;
                },
                multiple: false,
              }}
            />
            <YTHForm.Item
              name="handleDeadlineTime"
              title="处置期限"
              component={YTHDatePicker}
              disabled={modalType === 'view'}
              componentProps={{
                placeholder: '请输入',
                range: false,
                formatter: `YYYY-MM-DD HH:mm:ss`,
                precision: 'second',
                p_props: {
                  placeholder: ['处置期限'],
                },
              }}
            />
            <YTHForm.Item
              name="handleRequire"
              title="处置要求"
              component={TextArea}
              disabled={modalType === 'view'}
              mergeRow={1}
              required={false}
              componentProps={{
                placeholder: '请输入处置要求',
              }}
            />
          </>
        )}

        <YTHForm.Item
          name="attachments"
          title="结果照片"
          labelType={2}
          componentName="Upload"
          disabled={modalType === 'view'}
          componentProps={{
            listType: `yth-card`,
            name: 'file',
            action: '/gw/form-api/file/upload',
            headers: {
              authorization: Token(),
            },
            online: '/preview/onlinePreview',
            data: {
              formCode: 'task_attachments',
            },
          }}
          mergeRow={2}
        />
        <YTHForm.Item
          name="checkResultDescription"
          title="备注"
          labelType={2}
          component={TextArea}
          disabled={modalType === 'view'}
          componentProps={{
            placeholder: '请输入备注',
            rows: 3,
          }}
          mergeRow={2}
        />
      </YTHForm>
    </Modal>
  );
};

export default CheckPointEditModal;
